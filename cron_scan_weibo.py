import os
from subprocess import run, CalledProcessError, PIPE
import json
from datetime import datetime, timedelta
import mysql.connector
import time
import traceback
import threading

def cron_once():
    # 一次性的定时任务

    user_ids = ['2656274875']
    crawl_by_user_ids(user_ids)
    tasks = get_tasks()
    save2db(tasks, source=0)
    print(f'Cron job completed. {len(tasks)} tasks added to database.')



def cron_scan(start_now=False):
    # 定时任务
    # 每小时运行一次 cron_once()，当调用线程退出时自动退出

    def background_task():
        if not start_now:
            # Sleep for 1 hour (3600 seconds)
            time.sleep(3600)
        while True:
            try:
                print(f"[{datetime.now()}] Starting cron [SCAN_WEIBO] job...")
                cron_once()
                print(f"[{datetime.now()}] Cron job [SCAN_WEIBO] completed")
            except Exception as e:
                print(f"[{datetime.now()}] Error in cron job: {e}")
                traceback.print_exc()

            # Sleep for 1 day
            time.sleep(3600*24)

    # Create and start daemon thread
    # Daemon threads automatically exit when the main program exits
    cron_thread = threading.Thread(target=background_task, daemon=True)
    cron_thread.start()

    print("Cron background [SCAN_WEIBO] task started (runs every 1 day)")
    return cron_thread


    

def crawl_by_user_ids(user_ids):
    original_dir = os.getcwd()

    os.chdir(os.path.join('WeiboSpider', 'weibospider'))
    try:
        # Build command with separate arguments for each user ID
        cmd = ['python', 'run_spider.py', 'tweet_by_user_id', '--tweet_user_ids'] + user_ids
        result = run(cmd, check=True, stderr=PIPE, text=True)
    except CalledProcessError as e:
        print(f"Command failed with return code {e.returncode}")
        print(f"Command: {' '.join(e.cmd)}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if e.stdout:
            print(f"Standard output: {e.stdout}")
        raise  # Re-raise the exception
    finally:
        os.chdir(original_dir)

def get_tasks():
    file_dir = 'WeiboSpider/output/tweet_by_user_id_tweet.jsonl'
    tasks = []
    with open(file_dir, 'r') as f:
        for line in f:
            data = json.loads(line)
            tweet_id = data['mblogid']
            created_at = data['created_at']
            timestamp = int(datetime.datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S').timestamp())
            tasks.append({'tweet_id': tweet_id, 'created_at': timestamp})
    return tasks

def save2db(tasks, source: int):
    # MySQL Configuration (same as in cron_crawl.py)
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'cas',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }

    if not tasks:
        print("No tasks to save to database")
        return

    try:
        # Connect to database
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # Check which tweet_ids already exist in the database
        tweet_ids_to_check = [task['tweet_id'] for task in tasks]
        if tweet_ids_to_check:
            # Create placeholders for the IN clause
            placeholders = ', '.join(['%s'] * len(tweet_ids_to_check))
            check_query = f"SELECT tweet_id FROM tweet_task WHERE tweet_id IN ({placeholders})"
            cursor.execute(check_query, tweet_ids_to_check)
            existing_tweet_ids = set(row[0] for row in cursor.fetchall())
        else:
            existing_tweet_ids = set()

        # Filter out tasks with tweet_ids that already exist
        new_tasks = []
        skipped_count = 0
        for task in tasks:
            tweet_id = task['tweet_id']
            if tweet_id in existing_tweet_ids:
                skipped_count += 1
                print(f"Skipping tweet_id {tweet_id} - already exists in database")
            else:
                new_tasks.append(task)

        if not new_tasks:
            print(f"No new tasks to save. {skipped_count} tasks were skipped (already exist)")
            cursor.close()
            conn.close()
            return

        # Insert only new tasks into tweet_task table
        # id is auto_increment, so we don't include it in the INSERT
        insert_query = """
        INSERT INTO tweet_task (tweet_id, created_at, has_crawled, source)
        VALUES (%s, FROM_UNIXTIME(%s), %s, %s)
        """

        # Prepare data for batch insert
        insert_data = []
        for task in new_tasks:
            tweet_id = task['tweet_id']
            created_at = task['created_at']  # This is already a unix timestamp
            has_crawled = 0
            source = source
            insert_data.append((tweet_id, created_at, has_crawled, source))

        # Execute batch insert
        cursor.executemany(insert_query, insert_data)
        conn.commit()

        print(f"Successfully saved {len(new_tasks)} new tasks to database")
        if skipped_count > 0:
            print(f"{skipped_count} tasks were skipped (already exist)")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error saving tasks to database: {e}")
        if 'conn' in locals():
            conn.rollback()
            cursor.close()
            conn.close()

    

if __name__ == '__main__':
    cron_once()